# LeRobot 分布式训练指南

本指南介绍如何在LeRobot中使用多GPU进行分布式训练。

## 前提条件

1. **多GPU环境**: 确保你的机器有多个NVIDIA GPU
2. **NCCL支持**: 确保PyTorch安装了NCCL支持
3. **环境检查**: 运行 `nvidia-smi` 确认所有GPU可用

## 快速开始

### 方法1: 使用提供的脚本（推荐）

```bash
# 使用4个GPU训练ACT策略
./scripts/train_distributed.sh 4 \
    --dataset.repo_id=lerobot/pusht \
    --policy.type=act \
    --batch_size=32 \
    --steps=100000 \
    --policy.device=cuda \
    --wandb.enable=true

# 使用2个GPU训练Diffusion策略
./scripts/train_distributed.sh 2 \
    --dataset.repo_id=your_dataset \
    --policy.type=diffusion \
    --batch_size=64 \
    --steps=50000
```

### 方法2: 直接使用torchrun

```bash
# 4GPU训练示例
torchrun --standalone --nproc_per_node=4 \
    -m lerobot.scripts.train \
    --dataset.repo_id=lerobot/pusht \
    --policy.type=act \
    --batch_size=32 \
    --steps=100000 \
    --policy.device=cuda \
    --wandb.enable=true
```

## 重要配置说明

### 批次大小调整
- **单GPU批次大小**: 每个GPU处理的批次大小
- **有效批次大小**: `单GPU批次大小 × GPU数量`
- 建议根据GPU内存调整单GPU批次大小

### 学习率调整
分布式训练时，由于有效批次大小增加，可能需要调整学习率：

```bash
# 如果原来单GPU使用lr=1e-4, batch_size=32
# 现在4GPU使用batch_size=32（每GPU），有效batch_size=128
# 可以考虑将学习率调整为4e-4
--optimizer.lr=4e-4
```

### 数据集处理
- 数据集会自动在各GPU间分片
- 每个epoch每个GPU看到不同的数据子集
- 总的训练数据量保持不变

## 性能优化建议

### 1. 批次大小优化
```bash
# 根据GPU内存调整，避免OOM
# V100 32GB: batch_size=64-128
# A100 40GB: batch_size=128-256
# RTX 4090 24GB: batch_size=32-64
```

### 2. 数据加载优化
```bash
# 增加数据加载worker数量
--num_workers=8  # 根据CPU核心数调整
```

### 3. 混合精度训练
```bash
# 启用AMP以节省显存和加速训练
--policy.use_amp=true
```

## 监控和调试

### 1. 检查GPU利用率
```bash
# 训练过程中运行
watch -n 1 nvidia-smi
```

### 2. 日志查看
- 只有主进程(rank 0)会输出日志
- WandB日志也只从主进程上传
- 检查点保存也只在主进程进行

### 3. 常见问题

**问题1: NCCL超时**
```bash
# 设置更长的超时时间
export NCCL_TIMEOUT=1800
```

**问题2: 端口冲突**
```bash
# 指定不同的端口
torchrun --master_port=29501 --standalone --nproc_per_node=4 ...
```

**问题3: 显存不足**
- 减少batch_size
- 启用gradient checkpointing（如果策略支持）
- 使用更小的模型

## 性能对比

理论上，使用N个GPU应该获得接近N倍的训练速度提升：

| GPU数量 | 理论加速比 | 实际加速比* |
|---------|------------|-------------|
| 1       | 1x         | 1x          |
| 2       | 2x         | 1.8-1.9x    |
| 4       | 4x         | 3.5-3.8x    |
| 8       | 8x         | 6.5-7.5x    |

*实际加速比取决于模型大小、批次大小、网络带宽等因素

## 示例命令

### ACT策略训练（4GPU）
```bash
./scripts/train_distributed.sh 4 \
    --dataset.repo_id=your_dataset \
    --policy.type=act \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --batch_size=32 \
    --steps=300000 \
    --optimizer.lr=1e-4 \
    --policy.device=cuda \
    --wandb.enable=true \
    --wandb.project=distributed_training
```

### Diffusion策略训练（2GPU）
```bash
./scripts/train_distributed.sh 2 \
    --dataset.repo_id=your_dataset \
    --policy.type=diffusion \
    --batch_size=64 \
    --steps=100000 \
    --optimizer.lr=1e-4 \
    --policy.device=cuda \
    --policy.use_amp=true
```

## 注意事项

1. **随机种子**: 每个进程使用不同的随机种子以确保数据多样性
2. **检查点**: 只有主进程保存检查点，避免文件冲突
3. **评估**: 只在主进程进行评估以节省计算资源
4. **WandB**: 只有主进程上传日志到WandB
5. **EpisodeAwareSampler**: 分布式训练时会回退到普通的DistributedSampler

## 故障排除

如果遇到问题，请检查：
1. 所有GPU是否可用且内存充足
2. NCCL是否正确安装
3. 防火墙是否阻止了进程间通信
4. 是否有足够的共享内存（/dev/shm）

更多问题请参考PyTorch分布式训练文档。
